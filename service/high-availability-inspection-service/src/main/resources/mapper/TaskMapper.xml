<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.hacp.inspection.infrastructure.task.repository.mapper.TaskMapper">

    <!-- 分页查询任务列表，包含最新执行状态 -->
    <select id="pageQueryWithExecutionStatus" resultType="com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject.TaskDO">
        SELECT 
            t.id,
            t.workspace_id,
            t.task_id,
            t.name,
            t.description,
            t.status,
            t.created_by,
            t.created_by_name,
            t.created_time,
            t.updated_by,
            t.updated_by_name,
            t.updated_time,
            te.execution_status
        FROM inspection_task t
        LEFT JOIN inspection_task_execution te ON t.task_id = te.task_id 
            AND te.id = (
                SELECT MAX(id) 
                FROM inspection_task_execution 
                WHERE task_id = t.task_id
            )
        <where>
            <if test="name != null and name != ''">
                AND t.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="description != null and description != ''">
                AND t.description LIKE CONCAT('%', #{description}, '%')
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <if test="executionStatus != null">
                AND te.execution_status = #{executionStatus}
            </if>
            <if test="startTime != null and endTime != null">
                AND t.created_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ORDER BY t.id ASC
    </select>

</mapper>
