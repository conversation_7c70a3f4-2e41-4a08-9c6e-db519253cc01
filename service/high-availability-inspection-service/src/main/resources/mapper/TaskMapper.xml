<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.hacp.inspection.infrastructure.task.repository.mapper.TaskMapper">

    <!--
        Result Map: Task with Execution Status
        Maps task information including the latest execution status from LEFT JOIN
    -->
    <resultMap id="taskWithExecutionStatusResultMap" type="com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject.TaskDO">
        <!-- Primary Key Mapping - Critical for performance -->
        <id column="id" property="id" jdbcType="BIGINT"/>

        <!-- Basic Task Properties -->
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR"/>
        <result column="task_id" property="taskId" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>

        <!-- Audit Fields -->
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_by_name" property="createdByName" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_by_name" property="updatedByName" jdbcType="VARCHAR"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>

        <!-- Execution Status from JOIN - Non-table field -->
        <result column="execution_status" property="executionStatus" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
        t.workspace_id,
        t.task_id,
        t.name,
        t.description,
        t.status,
        t.created_by,
        t.created_by_name,
        t.created_time,
        t.updated_by,
        t.updated_by_name,
        t.updated_time
    </sql>

    <!--
        Query: Page Query with Execution Status
        Performance optimized query using LEFT JOIN with subquery
        Returns tasks with their latest execution status in a single query
    -->
    <select id="pageQueryWithExecutionStatus"
            resultMap="taskWithExecutionStatusResultMap"
            parameterType="map">
        SELECT
            <include refid="Base_Column_List"/>,
            te.execution_status
        FROM inspection_task t
        LEFT JOIN inspection_task_execution te ON t.task_id = te.task_id
            AND te.id = (
                SELECT MAX(id)
                FROM inspection_task_execution
                WHERE task_id = t.task_id
            )
        <where>
            <if test="name != null and name != ''">
                AND t.name LIKE CONCAT('%', #{name,jdbcType=VARCHAR}, '%')
            </if>
            <if test="description != null and description != ''">
                AND t.description LIKE CONCAT('%', #{description,jdbcType=VARCHAR}, '%')
            </if>
            <if test="status != null">
                AND t.status = #{status,jdbcType=TINYINT}
            </if>
            <if test="executionStatus != null">
                AND te.execution_status = #{executionStatus,jdbcType=VARCHAR}
            </if>
            <if test="startTime != null and endTime != null">
                AND t.created_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY t.id ASC
    </select>

</mapper>
