package com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.domain.model.enums.TaskStatus;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 巡检任务实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_task")
public class TaskDO extends BaseDO {
    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务状态
     */
    private TaskStatus status;

    /**
     * 执行状态（来自最新的执行记录，非数据库字段）
     */
    @TableField(exist = false)
    private ExecutionStatus executionStatus;
}
