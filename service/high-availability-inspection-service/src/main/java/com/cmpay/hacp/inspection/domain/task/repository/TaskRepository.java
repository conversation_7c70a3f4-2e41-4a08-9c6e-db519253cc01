package com.cmpay.hacp.inspection.domain.task.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cmpay.hacp.inspection.domain.model.task.InspectionTask;
import com.cmpay.hacp.inspection.domain.task.model.Task;

import javax.validation.constraints.NotNull;

public interface TaskRepository {
    Task getByTaskId(@NotNull String taskId);

    Task getByTaskName(@NotNull String taskName);

    void save(@NotNull Task task);

    boolean existsOtherTaskWithName(@NotNull String name, @NotNull String excludeTaskId);

    boolean updateTask(InspectionTask inspectionTask);

    boolean removeTask(@NotNull String taskId);

    IPage<Task> pageQuery(long current, long size, InspectionTask queryCondition);
}
