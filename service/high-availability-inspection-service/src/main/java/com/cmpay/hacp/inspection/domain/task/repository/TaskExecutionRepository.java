package com.cmpay.hacp.inspection.domain.task.repository;

import com.cmpay.hacp.inspection.domain.task.model.TaskExecution;

import javax.validation.constraints.NotNull;

public interface TaskExecutionRepository {

    /**
     * 保存任务执行记录
     *
     * @param taskExecution 任务执行记录
     */
    void save(@NotNull TaskExecution taskExecution);

    /**
     * 根据ID更新任务执行记录
     *
     * @param taskExecution 任务执行记录
     */
    void updateById(@NotNull TaskExecution taskExecution);

    /**
     * 根据执行ID查询任务执行记录
     *
     * @param executionId 执行ID
     * @return 任务执行记录
     */
    TaskExecution getByExecutionId(@NotNull String executionId);

    /**
     * 根据任务ID查询最新的任务执行记录
     *
     * @param taskId 任务ID
     * @return 最新的任务执行记录
     */
    TaskExecution getLatestByTaskId(@NotNull String taskId);
}
