package com.cmpay.hacp.inspection.infrastructure.task.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.domain.model.enums.TaskStatus;
import com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject.TaskDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

@Mapper
public interface TaskMapper extends BaseMapper<TaskDO> {

    /**
     * 分页查询任务列表，包含最新执行状态
     *
     * @param page 分页参数
     * @param name 任务名称（模糊查询）
     * @param description 任务描述（模糊查询）
     * @param status 任务状态
     * @param executionStatus 执行状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 任务分页结果
     */
    IPage<TaskDO> pageQueryWithExecutionStatus(
            Page<TaskDO> page,
            @Param("name") String name,
            @Param("description") String description,
            @Param("status") TaskStatus status,
            @Param("executionStatus") ExecutionStatus executionStatus,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );
}
