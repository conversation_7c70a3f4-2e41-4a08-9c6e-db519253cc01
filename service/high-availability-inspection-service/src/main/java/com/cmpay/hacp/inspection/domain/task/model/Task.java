package com.cmpay.hacp.inspection.domain.task.model;

import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.domain.model.enums.TaskStatus;
import lombok.Data;

@Data
public class Task {
    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务状态
     */
    private TaskStatus status;

    /**
     * 执行状态（来自最新的执行记录）
     */
    private ExecutionStatus executionStatus;

}
