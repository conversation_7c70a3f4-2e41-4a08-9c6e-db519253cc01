package com.cmpay.hacp.inspection.infrastructure.task.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.domain.task.model.TaskExecution;
import com.cmpay.hacp.inspection.domain.task.repository.TaskExecutionRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.TaskExecutionMapper;
import com.cmpay.hacp.inspection.infrastructure.task.converter.TaskExecutionConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * 任务执行状态Repository
 */
@Repository
@RequiredArgsConstructor
public class TaskExecutionRepositoryImpl implements TaskExecutionRepository {
    private final TaskExecutionMapper mapper;
    private final TaskExecutionConverter converter;



    @Override
    public void save(TaskExecution taskExecution) {
        TaskExecutionDO taskExecutionDO = converter.toTaskExecutionDO(taskExecution);
        mapper.insert(taskExecutionDO);
    }

    @Override
    public void updateById(TaskExecution taskExecution) {
        TaskExecutionDO taskExecutionDO = converter.toTaskExecutionDO(taskExecution);
        mapper.updateById(taskExecutionDO);
    }

    @Override
    public TaskExecution getByExecutionId(String executionId) {
        TaskExecutionDO taskExecutionDO = mapper.selectOne(Wrappers.lambdaQuery(TaskExecutionDO.class)
                .eq(TaskExecutionDO::getExecutionId, executionId));
        return converter.toTaskExecution(taskExecutionDO);
    }

    @Override
    public TaskExecution getLatestByTaskId(String taskId) {
        TaskExecutionDO taskExecutionDO = mapper.selectOne(Wrappers.lambdaQuery(TaskExecutionDO.class)
                .eq(TaskExecutionDO::getTaskId, taskId)
                .orderByDesc(TaskExecutionDO::getCreatedTime)
                .last("LIMIT 1"));
        return converter.toTaskExecution(taskExecutionDO);
    }
}
